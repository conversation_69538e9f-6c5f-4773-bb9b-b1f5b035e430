/* Modern Responsive Design for Frame Homes */

/* Large Tablet Styles */
@media (max-width: 1024px) {
    .header-container {
        padding: 0 20px;
    }

    /* Enhanced Hero Responsive */
    .hero-nav-dots {
        right: 20px;
        gap: 12px;
    }

    .nav-dot {
        width: 10px;
        height: 10px;
    }

    .hero-content {
        padding: 0 20px;
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto auto;
    }

    .house-specs {
        grid-column: 1;
        grid-row: 4;
        margin-left: 0;
        margin-top: 40px;
        max-width: 100%;
    }

    .hero-bottom {
        grid-column: 1;
        grid-row: 3;
        flex-direction: column;
        gap: 40px;
        text-align: center;
    }

    .hero-actions {
        justify-content: center;
    }

    .floating-stats {
        position: relative;
        top: auto;
        left: auto;
        flex-direction: row;
        justify-content: center;
        margin-top: 40px;
    }

    .hero-interactive {
        position: relative;
        margin-top: 40px;
    }

    /* Enhanced About Responsive */
    .about-grid {
        gap: 60px;
    }

    .feature-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }

    .image-composition {
        height: 500px;
    }

    .floating-elements {
        display: none;
    }



    .footer-main {
        gap: 60px;
    }

    .footer-links {
        gap: 40px;
    }
}

/* Tablet Styles */
@media (max-width: 768px) {
    .container {
        padding: 0 20px;
    }

    /* Header */
    .header-container {
        padding: 0 20px;
    }

    /* Custom logo responsive adjustments */
    .header-logo .custom-logo {
        height: 36px;
        max-height: 36px;
        max-width: 180px;
    }

    .header-info {
        display: none;
    }

    .header-nav {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: white;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding: 60px 20px;
        transition: left 0.3s ease;
        z-index: 999;
    }

    .header-nav.active {
        left: 0;
    }

    .nav-menu {
        flex-direction: column;
        gap: 40px;
        margin-bottom: 40px;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .mobile-menu-toggle.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-5px, 6px);
    }

    .mobile-menu-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .mobile-menu-toggle.active span:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px);
    }

    /* Enhanced Hero Mobile */
    .modern-hero {
        margin-top: 80px;
        height: 100vh;
        min-height: 100vh;
    }

    .hero-nav-dots {
        display: none;
    }

    .hero-content {
        padding: 0 20px;
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto auto;
    }

    .hero-badge {
        margin-top: 100px;
        text-align: center;
    }

    .hero-main {
        margin-top: 40px;
        text-align: center;
    }

    .hero-title {
        font-size: clamp(2.5rem, 10vw, 4rem);
        text-align: center;
    }

    .hero-subtitle {
        text-align: center;
    }

    .house-specs {
        grid-column: 1;
        grid-row: 4;
        margin-left: 0;
        margin-top: 40px;
        max-width: 100%;
    }

    .hero-bottom {
        grid-column: 1;
        grid-row: 3;
        flex-direction: column;
        gap: 40px;
        text-align: center;
        margin-bottom: 40px;
    }

    .hero-actions {
        justify-content: center;
        gap: 40px;
    }

    .showreel-section {
        align-items: center;
    }

    .house-model {
        display: none;
    }

    .floating-stats {
        position: relative;
        top: auto;
        left: auto;
        flex-direction: row;
        justify-content: center;
        margin: 40px 0;
    }

    .hero-particles {
        display: none;
    }

    /* Sections */
    section {
        padding: 80px 0;
    }

    .section-title {
        font-size: clamp(2rem, 6vw, 3rem);
    }

    /* Enhanced About Mobile */
    .about-grid {
        grid-template-columns: 1fr;
        gap: 60px;
    }

    .about-content .section-header {
        text-align: center;
    }

    .feature-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .feature-item {
        padding: 20px;
        text-align: center;
    }

    .feature-icon {
        margin: 0 auto 16px;
    }

    .about-actions {
        justify-content: center;
        flex-wrap: wrap;
        gap: 20px;
    }

    .image-composition {
        height: 400px;
    }

    .floating-elements {
        display: none;
    }

    .decorative-elements {
        display: none;
    }



    /* Services */
    .services-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    /* Projects */
    .projects-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    /* Footer */
    .footer-main {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .footer-links {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .footer-bottom-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .footer-bottom-links {
        justify-content: center;
    }

    /* Enhanced Modern Footer Responsive */
    .footer-top-nav {
        flex-direction: column;
        gap: 20px;
        text-align: center;
        padding: 30px 0;
    }

    .footer-nav-menu {
        gap: 20px;
    }

    .footer-cta-section {
        flex-direction: column;
        text-align: center;
        gap: 30px;
        margin-bottom: 80px;
        padding-bottom: 40px;
    }

    .footer-cta-title {
        font-size: 36px;
    }

    .footer-email-link {
        font-size: 24px;
    }

    .footer-links-grid {
        grid-template-columns: 1fr;
        gap: 40px;
        margin-bottom: 60px;
    }

    .footer-large-brand {
        font-size: clamp(40px, 10vw, 80px);
    }
}

/* Landscape Mobile Adjustments */
@media (max-width: 768px) and (orientation: landscape) {
    .modern-hero {
        height: 100vh;
        min-height: 100vh;
    }

    .hero-content {
        grid-template-rows: auto auto auto;
    }

    .hero-badge {
        margin-top: 60px;
    }

    .hero-main {
        margin-top: 20px;
    }

    .hero-bottom {
        margin-bottom: 20px;
    }
}

/* Very Small Screens */
@media (max-width: 320px) {
    .modern-hero {
        min-height: 100vh;
    }
}

/* Large Screens */
@media (min-width: 1400px) {
    .modern-hero {
        height: 100vh;
        min-height: 100vh;
    }
}

/* Mobile Styles */
@media (max-width: 480px) {
    .container {
        padding: 0 16px;
    }

    .header-container {
        padding: 0 16px;
    }

    .modern-header {
        padding: 16px 0;
    }

    /* Mobile Hero Height Adjustments */
    .modern-hero {
        height: 100vh;
        min-height: 100vh;
        margin-top: 70px;
    }

    /* Mobile custom logo adjustments */
    .header-logo .custom-logo {
        height: 32px;
        max-height: 32px;
        max-width: 150px;
    }

    .logo-text {
        font-size: 20px;
    }

    .hero-content {
        padding: 0 16px;
    }

    .hero-badge {
        margin-top: 80px;
    }

    .hero-title {
        font-size: clamp(2rem, 12vw, 3rem);
        line-height: 1;
    }

    .house-specs {
        padding: 24px;
    }

    .specs-content h3 {
        font-size: 20px;
    }

    .specs-content p {
        font-size: 13px;
    }

    .hero-tagline h2 {
        font-size: clamp(1.5rem, 8vw, 2rem);
    }

    .hero-actions {
        gap: 30px;
    }

    .showreel-button {
        flex-direction: column;
        gap: 8px;
    }

    /* Sections */
    section {
        padding: 60px 0;
    }

    .section-header {
        margin-bottom: 60px;
    }

    .section-title {
        font-size: clamp(1.8rem, 8vw, 2.5rem);
    }

    .section-subtitle {
        font-size: 16px;
    }

    /* About */
    .about-description {
        font-size: 16px;
    }

    .feature-item {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .about-actions {
        flex-direction: column;
        gap: 20px;
    }



    /* Services */
    .services-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }

    .service-content {
        padding: 24px;
    }

    .service-content h3 {
        font-size: 20px;
    }

    /* Dark/Light Section Adjustments */
    .dark-bg-overlay {
        opacity: 0.95;
    }

    .section-badge {
        padding: 8px 16px;
        font-size: 10px;
    }

    .section-nav-links {
        flex-direction: column;
        gap: 12px;
        align-items: center;
    }

    .section-nav-link {
        font-size: 13px;
        padding: 6px 14px;
    }

    /* Projects */
    .project-card {
        height: 300px;
    }

    .project-overlay {
        padding: 24px;
    }

    .project-info h3 {
        font-size: 24px;
    }

    /* Footer */
    .modern-footer {
        padding: 60px 0 30px;
    }

    .footer-brand {
        text-align: center;
    }

    .footer-logo {
        justify-content: center;
    }

    .footer-column {
        text-align: center;
    }

    .social-links {
        justify-content: center;
    }

    /* Enhanced Modern Footer Mobile */
    .footer-top-nav {
        padding: 20px 0;
    }

    .footer-nav-menu {
        flex-direction: column;
        gap: 15px;
    }

    .footer-nav-link {
        font-size: 12px;
    }

    .footer-cta-section {
        margin-bottom: 60px;
        padding-bottom: 30px;
    }

    .footer-cta-title {
        font-size: 28px;
        letter-spacing: 1px;
    }

    .footer-email-link {
        font-size: 18px;
        word-break: break-all;
    }

    .footer-links-grid {
        gap: 30px;
        margin-bottom: 40px;
    }

    .footer-column-title {
        font-size: 12px;
        margin-bottom: 20px;
    }

    .footer-links-list a {
        font-size: 13px;
    }

    .contact-item {
        gap: 2px;
    }

    .contact-label {
        font-size: 11px;
    }

    .contact-value {
        font-size: 13px;
    }

    .footer-large-brand {
        font-size: clamp(32px, 15vw, 60px);
        margin-bottom: 40px;
    }

    .footer-bottom-section {
        padding: 20px 0;
    }

    .footer-bottom-links {
        gap: 15px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .footer-bottom-link {
        font-size: 11px;
    }

    /* Modal */
    .modal-content {
        margin: 10% auto;
        width: 95%;
    }

    .video-container {
        height: 250px;
    }

    .modal-close {
        top: 10px;
        right: 20px;
        font-size: 24px;
    }
}

/* Landscape Mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .modern-hero {
        height: 100vh;
    }

    .hero-content {
        grid-template-rows: auto 1fr auto;
    }

    .house-specs {
        display: none;
    }

    .hero-actions {
        flex-direction: row;
        justify-content: center;
    }
}

/* Print Styles */
@media print {

    .modern-header,
    .modern-hero,
    .modern-footer,
    .btn-modern,
    .play-btn {
        display: none !important;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
        background: white;
    }

    .container {
        max-width: none;
        padding: 0;
    }

    section {
        padding: 20px 0;
        page-break-inside: avoid;
    }

    .section-title {
        font-size: 18pt;
        color: #000;
    }

    .service-card,
    .project-card {
        border: 1px solid #ccc;
        margin-bottom: 20px;
        page-break-inside: avoid;
    }

    img {
        max-width: 100% !important;
        height: auto !important;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {

    .logo,
    .footer-logo img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .scroll-arrow {
        animation: none;
    }
}

/* Focus Styles for Accessibility */
@media (any-hover: hover) {

    .btn-modern:focus,
    .nav-link:focus,
    .social-links a:focus,
    .play-btn:focus {
        outline: 2px solid #d4a574;
        outline-offset: 2px;
    }
}