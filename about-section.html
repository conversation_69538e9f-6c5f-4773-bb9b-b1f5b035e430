<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1" name="viewport" />
    <title>
        Photography Page
    </title>
    <script src="https://cdn.tailwindcss.com">
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&amp;display=swap" rel="stylesheet" />
    <style>
        body {
            font-family: "Inter", sans-serif;
        }
    </style>
</head>

<body class="bg-[#18171d] text-white min-h-screen p-6 md:p-12">
    <div class="max-w-7xl mx-auto">
        <!-- Top Section -->
        <div class="flex flex-col md:flex-row md:justify-between md:items-start gap-16 md:gap-0">
            <!-- Left side small text and paragraph -->
            <div class="flex flex-col space-y-8 md:max-w-xs">
                <p class="text-[13px] text-[#9a9a9a] font-normal">
                    (01)
                </p>
                <p class="text-[14px] font-normal">
                    About Us
                </p>
                <p class="text-[13px] text-[#7a7a7a] font-normal leading-tight max-w-[280px]">
                    We Ardently Strive To Encapsulate Life’s Most Precious Moments, Weaving A Tapestry Of Artistry And A
                    Hint Of Enchanting Magic With The Timeless Essence Of The Human Experience.
                </p>
            </div>
            <!-- Right side big text and button -->
            <div class="flex flex-col max-w-lg md:max-w-xl space-y-8">
                <h1 class="text-3xl md:text-4xl font-normal leading-[1.3] max-w-xl">
                    <span class="font-semibold">
                        Photography
                    </span>
                    is driven by a deep passion for
                    <span class="font-semibold">
                        capturing life’s
                    </span>
                    most
                    <span class="font-semibold">
                        precious moments
                    </span>
                    with artistry and a touch of magic
                </h1>
                <div class="flex items-center space-x-4">
                    <button
                        class="border border-white rounded-full px-6 py-2 text-[13px] font-normal tracking-wide hover:bg-white hover:text-black transition-colors duration-300">
                        LEARN MORE
                    </button>
                    <button aria-label="Arrow right"
                        class="bg-white text-black rounded-full w-10 h-10 flex items-center justify-center hover:bg-gray-200 transition-colors duration-300">
                        <i class="fas fa-arrow-up-right">
                        </i>
                    </button>
                </div>
            </div>
        </div>
        <!-- Bottom Section -->
        <div
            class="mt-20 bg-[#323532] rounded-xl p-8 md:p-12 flex flex-col md:flex-row md:justify-between md:items-start gap-8 md:gap-0">
            <!-- Left text block -->
            <div class="flex flex-col md:max-w-lg space-y-6">
                <p class="text-[13px] text-[#9a9a9a] font-normal">
                    (02)
                </p>
                <p class="text-[14px] font-semibold">
                    Our Expertise
                </p>
                <h2 class="text-3xl md:text-4xl font-normal leading-[1.2] max-w-md">
                    When moments captured every dreams crafted into beautiful reality
                </h2>
                <div class="mt-auto max-w-[280px]">
                    <p class="text-[14px] font-normal">
                        Chasing Clouds at Mont Blanc’s Pinnacle
                    </p>
                    <p class="text-[13px] text-[#9a9a9a] font-normal">
                        Mont Blanc, France
                    </p>
                </div>
            </div>
            <!-- Right side images and categories -->
            <div class="flex flex-col md:flex-row md:space-x-6 w-full md:w-auto">
                <!-- Images container -->
                <div class="flex space-x-4 overflow-x-auto md:overflow-visible rounded-lg md:rounded-none">
                    <img alt="Mountain landscape with trees and cliffs under a clear sky"
                        class="rounded-lg flex-shrink-0 w-[400px] h-[250px] object-cover" height="250"
                        src="https://storage.googleapis.com/a1aa/image/40417184-821e-4588-8b1f-145c2ee66064.jpg"
                        width="400" />
                    <img alt="Lake with boats and mountains in the background under a cloudy sky"
                        class="rounded-lg flex-shrink-0 w-[200px] h-[250px] object-cover" height="250"
                        src="https://storage.googleapis.com/a1aa/image/35238963-5d8e-4a99-3e2e-9298c1366164.jpg"
                        width="200" />
                </div>
                <!-- Categories -->
                <div class="hidden md:flex flex-col ml-8 space-y-4 text-right min-w-[120px]">
                    <p class="font-normal text-[14px] text-white">
                        Landscape
                    </p>
                    <p class="font-normal text-[14px] text-[#7a7a7a]">
                        Wildlife
                    </p>
                    <p class="font-normal text-[14px] text-[#7a7a7a]">
                        Architectural
                    </p>
                    <p class="font-normal text-[14px] text-[#7a7a7a]">
                        Travel
                    </p>
                    <p class="font-normal text-[14px] text-[#7a7a7a]">
                        Potrait
                    </p>
                </div>
            </div>
            <!-- Navigation arrows and see all -->
            <div class="flex items-center space-x-4 mt-8 md:mt-0 md:flex-col md:space-x-0 md:space-y-4 md:ml-8">
                <button aria-label="Previous"
                    class="border border-white rounded-full w-10 h-10 flex items-center justify-center text-white hover:bg-white hover:text-black transition-colors duration-300">
                    <i class="fas fa-chevron-left">
                    </i>
                </button>
                <button aria-label="Next"
                    class="bg-white text-black rounded-full w-10 h-10 flex items-center justify-center hover:bg-gray-200 transition-colors duration-300">
                    <i class="fas fa-chevron-right">
                    </i>
                </button>
                <p class="text-[13px] font-normal flex items-center space-x-1 whitespace-nowrap">
                    <span>
                        See all
                    </span>
                    <i class="fas fa-long-arrow-alt-right">
                    </i>
                </p>
            </div>
        </div>
    </div>
</body>

</html>