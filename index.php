<?php
require_once 'config/config.php';
require_once 'includes/color-scheme.php';

// Get dynamic content
$heroContent = $db->fetchAll("SELECT * FROM content WHERE section_key LIKE 'hero_%' AND is_active = 1");
$aboutContent = $db->fetchOne("SELECT * FROM content WHERE section_key = 'about_intro' AND is_active = 1");
$featuredServices = $db->fetchAll("SELECT * FROM services WHERE is_featured = 1 AND is_active = 1 ORDER BY sort_order ASC LIMIT 5");
$featuredProjects = $db->fetchAll("SELECT * FROM projects WHERE is_featured = 1 AND is_active = 1 ORDER BY sort_order ASC LIMIT 6");
$testimonials = $db->fetchAll("SELECT * FROM testimonials WHERE is_featured = 1 AND is_approved = 1 ORDER BY sort_order ASC LIMIT 5");

// Convert hero content to associative array
$hero = [];
foreach ($heroContent as $item) {
    $hero[$item['section_key']] = $item;
}

// Get custom logo from branding settings
$logoSettings = $db->fetchOne("SELECT setting_value FROM site_settings WHERE setting_key = 'branding_logo'");
$customLogo = $logoSettings ? json_decode($logoSettings['setting_value'], true) : null;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= SITE_NAME ?> - Precision-Built Frame Homes for Modern Living</title>
    <meta name="description" content="Flori Construction Ltd specializes in precision-built frame homes for modern living. Discover our contemporary architectural solutions and sustainable building practices.">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= ASSETS_URL ?>/images/favicon.ico">

    <!-- CSS -->
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/style.css">
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/responsive.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Dynamic Color Scheme -->
    <?= generateColorSchemeCSS() ?>
    <style>
        body {
            background-color: #18171d;
            color: white;
            font-family: "Inter", sans-serif;
            min-height: 100vh;
        }

        .container {
            max-width: 1120px;
            margin: 0 auto;
        }

        /* Top Section */
        .top-section {
            display: flex;
            flex-direction: column;
            gap: 64px;
        }

        @media (min-width: 768px) {
            .top-section {
                flex-direction: row;
                justify-content: space-between;
                align-items: flex-start;
            }
        }

        .top-left {
            max-width: 280px;
            display: flex;
            flex-direction: column;
            gap: 32px;
        }

        .top-left small {
            font-size: 13px;
            color: #9a9a9a;
            font-weight: 400;
            margin: 0;
        }

        .top-left .title {
            font-size: 14px;
            font-weight: 400;
            margin: 0;
        }

        .top-left p.description {
            font-size: 13px;
            color: #ffffff;
            font-weight: 400;
            line-height: 1.3;
            margin: 0;
        }

        .top-right {
            max-width: 420px;
            display: flex;
            flex-direction: column;
            gap: 32px;
        }

        .top-right h1 {
            font-weight: 400;
            font-size: 28px;
            line-height: 1.3;
            margin: 0;
            max-width: 420px;
        }

        @media (min-width: 768px) {
            .top-right h1 {
                font-size: 36px;
            }
        }

        .top-right h1 strong {
            font-weight: 600;
        }

        .btn-group {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .btn-learn-more {
            border: 1px solid white;
            border-radius: 9999px;
            padding: 8px 24px;
            font-size: 13px;
            font-weight: 400;
            color: white;
            background: transparent;
            cursor: pointer;
            transition: background-color 0.3s, color 0.3s;
        }

        .btn-learn-more:hover {
            background-color: white;
            color: black;
        }

        .btn-arrow {
            width: 40px;
            height: 40px;
            border-radius: 9999px;
            background-color: white;
            color: black;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s;
        }

        .btn-arrow:hover {
            background-color: #e5e5e5;
        }

        /* Bottom Section */
        .bottom-section {
            margin-top: 80px;
            background-color: #323532;
            border-radius: 16px;
            padding: 32px 48px;
            display: flex;
            flex-direction: column;
            gap: 32px;
        }

        @media (min-width: 768px) {
            .bottom-section {
                flex-direction: row;
                justify-content: space-between;
                align-items: flex-start;
            }
        }

        .bottom-left {
            max-width: 420px;
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .bottom-left small {
            font-size: 13px;
            color: #9a9a9a;
            font-weight: 400;
            margin: 0;
        }

        .bottom-left .subtitle {
            font-size: 14px;
            font-weight: 600;
            margin: 0;
        }

        .bottom-left h2 {
            font-weight: 400;
            font-size: 28px;
            line-height: 1.2;
            margin: 0;
            max-width: 400px;
        }

        @media (min-width: 768px) {
            .bottom-left h2 {
                font-size: 36px;
            }
        }

        .bottom-left .caption {
            margin-top: auto;
            max-width: 280px;
        }

        .bottom-left .caption p {
            margin: 0;
        }

        .bottom-left .caption p.title {
            font-size: 14px;
            font-weight: 400;
        }

        .bottom-left .caption p.location {
            font-size: 13px;
            color: #9a9a9a;
            font-weight: 400;
        }

        /* Bottom right container */
        .bottom-right {
            display: flex;
            flex-direction: column;
            gap: 24px;
            width: 100%;
        }

        @media (min-width: 768px) {
            .bottom-right {
                flex-direction: row;
                width: auto;
            }
        }

        /* Images container */
        .images-container {
            display: flex;
            gap: 16px;
            overflow-x: auto;
            border-radius: 12px;
            flex-shrink: 0;
        }

        @media (min-width: 768px) {
            .images-container {
                overflow: visible;
                border-radius: 0;
            }
        }

        .images-container img {
            border-radius: 12px;
            object-fit: cover;
            flex-shrink: 0;
            height: 250px;
        }

        .images-container img:first-child {
            width: 400px;
        }

        .images-container img:last-child {
            width: 200px;
        }

        /* Categories */
        .categories {
            display: none;
            flex-direction: column;
            margin-left: 32px;
            gap: 16px;
            min-width: 120px;
            text-align: right;
        }

        @media (min-width: 768px) {
            .categories {
                display: flex;
            }
        }

        .categories p {
            font-size: 14px;
            font-weight: 400;
            margin: 0;
            color: #7a7a7a;
            cursor: default;
            user-select: none;
        }

        .categories p.active {
            color: white;
            font-weight: 400;
        }

        /* Navigation arrows and see all */
        .nav-seeall {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-top: 32px;
            flex-wrap: nowrap;
        }

        @media (min-width: 768px) {
            .nav-seeall {
                flex-direction: column;
                margin-top: 0;
                margin-left: 32px;
                gap: 16px;
            }
        }

        .nav-button {
            width: 40px;
            height: 40px;
            border-radius: 9999px;
            border: 1px solid white;
            background: transparent;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s, color 0.3s;
        }

        .nav-button:hover {
            background-color: white;
            color: black;
        }

        .nav-button.next {
            background-color: white;
            color: black;
            border: none;
        }

        .nav-button.next:hover {
            background-color: #e5e5e5;
        }

        .see-all-text {
            font-size: 13px;
            font-weight: 400;
            display: flex;
            align-items: center;
            gap: 4px;
            white-space: nowrap;
            user-select: none;
            cursor: default;
        }
    </style>
</head>
<body>
    <!-- Modern Header -->
    <header class="modern-header">
        <div class="header-container">
            <!-- Logo -->
            <div class="header-logo">
                <?php if ($customLogo && isset($customLogo['logo_path']) && !empty($customLogo['logo_path'])): ?>
                    <!-- Custom Logo from Admin Branding -->
                    <img src="<?= UPLOAD_URL . '/' . $customLogo['logo_path'] ?>"
                         alt="<?= SITE_NAME ?>"
                         class="logo custom-logo"
                         onerror="this.style.display='none'; document.querySelector('.default-logo-fallback').style.display='flex';">
                    <!-- Fallback for broken custom logo -->
                    <div class="default-logo-fallback" style="display: none;">
                        <img src="<?= ASSETS_URL ?>/images/logo.png" alt="<?= SITE_NAME ?>" class="logo">
                        <span class="logo-text">Logo</span>
                    </div>
                <?php else: ?>
                    <!-- Default Logo -->
                    <img src="<?= ASSETS_URL ?>/images/logo.png" alt="<?= SITE_NAME ?>" class="logo">
                    <span class="logo-text">Logo</span>
                <?php endif; ?>
            </div>

            <!-- Phone and Mobile Info -->
            <div class="header-info">
                <div class="contact-info">
                    <a href="tel:02089147883" class="phone">PHONE: 0208 914 7883</a>
                    <a href="tel:07882923621" class="mobile">MOBILE: 078 8292 3621</a>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="header-nav">
                <ul class="nav-menu">
                    <li><a href="index.php" class="nav-link active">HOME</a></li>
                    <li><a href="about.php" class="nav-link">ABOUT</a></li>
                    <li><a href="services.php" class="nav-link">SERVICES</a></li>
                     <li class="dropdown">
                        <a href="#" class="nav-link dropdown-toggle">Our Projects <i class="fas fa-chevron-down"></i></a>
                        <ul class="dropdown-menu">
                            <li><a href="projects.php?type=completed">Completed Projects</a></li>
                            <li><a href="projects.php?type=ongoing">Ongoing Projects</a></li>
                        </ul>
                    </li>
                    <li><a href="media.php" class="nav-link">MEDIA</a></li>
                </ul>
                <button onclick="window.location.href='contact.php'" class="btn">GET QUOTE</button>
            </nav>

            <!-- Mobile Menu Toggle -->
            <button class="mobile-menu-toggle">
                <span></span>
                <span></span>
                <span></span>
            </button>
        </div>
    </header>

    <!-- Enhanced Modern Hero Section -->
    <section class="modern-hero" id="modern-hero">
        <!-- Background with Parallax Effect -->
        <div class="hero-background">
            <div class="hero-image-container">
                <img src="<?= ASSETS_URL ?>/images/banner3.png" alt="Modern Frame House" class="hero-image">
                <div class="hero-overlay"></div>
                <div class="hero-gradient"></div>
            </div>
        </div>

        <!-- Floating Navigation Dots -->
        <div class="hero-nav-dots">
            <div class="nav-dot active" data-section="modern-hero" title="Home"></div>
            <div class="nav-dot" data-section="modern-about" title="About"></div>
            <div class="nav-dot" data-section="modern-services" title="Services"></div>
            <div class="nav-dot" data-section="modern-projects" title="Projects"></div>
            <div class="nav-dot" data-section="modern-testimonials" title="Testimonials"></div>
        </div>

        <div class="hero-content">


            <!-- Main Hero Text with Stagger Animation -->
            <div class="hero-main">
                <h1 class="hero-title">
                    <span class="title-line" data-delay="0" style="color: white;">EXPERIENCE</span>
                    <span class="title-line highlight" data-delay="200">MATTERS</span>
                </h1>
                <div class="hero-subtitle">
                    <p>Quality, professionalism, and unwavering dedication to customer satisfaction in every project we deliver</p>
                </div>
            </div>


            <!-- Enhanced Bottom Section -->
            <div class="hero-bottom">
                <div class="hero-tagline">
                    <h2 style="color: white;">Do it with <em>passion</em><br>or not at all</h2>
                    <div class="tagline-accent"></div>
                </div>

                <div class="hero-actions">
                    <div class="showreel-section">
                        <div class="showreel-button">
                            <button class="play-btn" onclick="playShowreel()">
                                <div class="play-icon">
                                    <i class="fas fa-play"></i>
                                </div>
                                <span>PLAY VIDEO </span>
                            </button>
                            <div class="showreel-info">
                            </div>
                        </div>
                    </div>
                </div>
            </div>




        </div>


        <!-- Ambient Particles -->
        <div class="hero-particles">
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
        </div>
    </section>

    <!-- About Section -->
    <div class="container">
        <!-- Top Section -->
        <section class="top-section" aria-label="About Us and Our Story">
            <div class="top-left">
                <small>(01)</small>
                <p class="title">About Us</p>
                <p class="description">
                    We Ardently Strive To Encapsulate Life’s Most Precious Moments, Weaving A Tapestry Of Artistry And A
                    Hint Of Enchanting Magic With The Timeless Essence Of The Human Experience.
                </p>
            </div>
            <div class="top-right">
                <h1>
                    <strong>Photography</strong> is driven by a deep passion for <strong>capturing life’s</strong> most
                    <strong>precious moments</strong> with artistry and a touch of magic
                </h1>
                <div class="btn-group">
                    <button class="btn-learn-more" type="button">LEARN MORE</button>
                    <button class="btn-arrow" aria-label="Arrow right" type="button">
                        <i class="fas fa-arrow-up-right"></i>
                    </button>
                </div>
            </div>
        </section>
        <!-- Bottom Section -->
        <section class="bottom-section" aria-label="Our Expertise and Gallery">
            <div class="bottom-left">
                <small>(02)</small>
                <p class="subtitle">Our Expertise</p>
                <h2>When construction dreams are crafted into beautiful reality</h2>
                <div class="caption">
                    <p class="title">Chasing Clouds at Mont Blanc’s Pinnacle</p>
                    <?php if (!empty($featuredServices)): ?>
                    <?php $firstService = $featuredServices[0]; ?>
                    <p class="location"><?= htmlspecialchars($firstService['short_description']) ?></p>
                <?php else: ?>
                    <p class="location">Quality craftsmanship and professional expertise</p>
                <?php endif; ?>
                </div>
            </div>
            <div class="bottom-right">
                <div class="images-container" aria-label="Service gallery images">
                    <?php if (!empty($featuredServices)): ?>
                        <?php
                        // Get first two services for display
                        $displayServices = array_slice($featuredServices, 0, 2);
                        $imageCount = 0;
                        ?>
                        <?php foreach ($displayServices as $service): ?>
                            <?php
                            $serviceImage = $service['featured_image'] ? UPLOAD_URL . '/' . $service['featured_image'] : ASSETS_URL . '/images/service-default.jpg';
                            $imageWidth = $imageCount === 0 ? '400' : '200';
                            ?>
                            <img src="<?= $serviceImage ?>"
                                alt="<?= htmlspecialchars($service['title']) ?>"
                                width="<?= $imageWidth ?>"
                                height="250"
                                data-service-id="<?= $service['id'] ?>" />
                            <?php $imageCount++; ?>
                        <?php endforeach; ?>

                        <?php if (count($displayServices) < 2): ?>
                            <!-- Fallback second image if only one service -->
                            <img src="<?= ASSETS_URL ?>/images/becanham.jpg"
                                alt="Construction detail showing quality craftsmanship"
                                width="200" height="250" />
                        <?php endif; ?>
                    <?php else: ?>
                        <!-- Fallback images if no services -->
                        <img src="<?= ASSETS_URL ?>/images/banner3.png"
                            alt="Modern construction project with architectural excellence" width="400" height="250" />
                        <img src="<?= ASSETS_URL ?>/images/becanham.jpg"
                            alt="Construction detail showing quality craftsmanship" width="200" height="250" />
                    <?php endif; ?>
                </div>
                <div class="nav-seeall" aria-label="Gallery navigation and see all">
                    <button class="nav-button prev" aria-label="Previous service" type="button" onclick="navigateServices('prev')">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="nav-button next" aria-label="Next service" type="button" onclick="navigateServices('next')">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                    <p class="see-all-text" onclick="window.location.href='services.php'" style="cursor: pointer;">
                        <span>See all</span>
                        <i class="fas fa-long-arrow-alt-right"></i>
                    </p>
                </div>
            </div>
        </section>
    </div>

    <!-- Modern Services Section -->
    <section class="modern-services" id="modern-services">
        <div class="container">
            <div class="section-header">
                <span class="section-badge">OUR EXPERTISE</span>
                <h2 class="section-title">Construction Solutions</h2>
                <p class="section-subtitle">We are committed to delivering high-quality services in these areas and more. Our goal is to provide reliable and efficient solutions that meet the unique needs of each project.</p>
                <div class="section-nav-links">
                    <a href="#modern-about" class="section-nav-link">
                        <i class="fas fa-arrow-up"></i> About Us
                    </a>
                    <a href="#modern-projects" class="section-nav-link">
                        <i class="fas fa-arrow-down"></i> Our Projects
                    </a>
                </div>
            </div>

            <div class="services-grid">
                <?php if (!empty($featuredServices)): ?>
                    <?php foreach ($featuredServices as $service): ?>
                    <div class="service-card">
                        <div class="service-image">
                            <img src="<?= $service['featured_image'] ? UPLOAD_URL . '/' . $service['featured_image'] : ASSETS_URL . '/images/service-default.jpg' ?>"
                                 alt="<?= htmlspecialchars($service['title']) ?>">
                            <div class="service-overlay">
                                <div class="service-icon">
                                    <i class="fas fa-home"></i>
                                </div>
                            </div>
                        </div>
                        <div class="service-content">
                            <h3><?= htmlspecialchars($service['title']) ?></h3>
                            <p><?= htmlspecialchars($service['short_description']) ?></p>
                            <a href="service.php?slug=<?= $service['slug'] ?>" class="service-link">
                                Explore <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <!-- Default Services if none in database -->
                    <div class="service-card">
                        <div class="service-image">
                            <img src="<?= ASSETS_URL ?>/images/civil-engineering.jpg" alt="Civil Engineering">
                            <div class="service-overlay">
                                <div class="service-icon">
                                    <i class="fas fa-hard-hat"></i>
                                </div>
                            </div>
                        </div>
                        <div class="service-content">
                            <h3>Civil Engineering</h3>
                            <p>Professional civil engineering solutions with extensive industry experience and expertise.</p>
                            <a href="services.php" class="service-link">
                                Explore <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>

                    <div class="service-card">
                        <div class="service-image">
                            <img src="<?= ASSETS_URL ?>/images/groundworks.jpg" alt="Groundworks">
                            <div class="service-overlay">
                                <div class="service-icon">
                                    <i class="fas fa-tools"></i>
                                </div>
                            </div>
                        </div>
                        <div class="service-content">
                            <h3>Professional Groundworks</h3>
                            <p>Expert groundwork services with precision, efficiency and commitment to quality.</p>
                            <a href="services.php" class="service-link">
                                Explore <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>

                    <div class="service-card">
                        <div class="service-image">
                            <img src="<?= ASSETS_URL ?>/images/rc-frames.jpg" alt="RC Frames">
                            <div class="service-overlay">
                                <div class="service-icon">
                                    <i class="fas fa-building"></i>
                                </div>
                            </div>
                        </div>
                        <div class="service-content">
                            <h3>RC Frames Construction</h3>
                            <p>Reinforced concrete frame construction with superior structural integrity and durability.</p>
                            <a href="services.php" class="service-link">
                                Explore <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>

                    <div class="service-card">
                        <div class="service-image">
                            <img src="<?= ASSETS_URL ?>/images/basement.jpg" alt="Basement Construction">
                            <div class="service-overlay">
                                <div class="service-icon">
                                    <i class="fas fa-home"></i>
                                </div>
                            </div>
                        </div>
                        <div class="service-content">
                            <h3>Basement Construction</h3>
                            <p>Specialized basement construction services with professional expertise and quality assurance.</p>
                            <a href="services.php" class="service-link">
                                Explore <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>

                    <div class="service-card">
                        <div class="service-image">
                            <img src="<?= ASSETS_URL ?>/images/hard-landscaping.jpg" alt="Hard Landscaping">
                            <div class="service-overlay">
                                <div class="service-icon">
                                    <i class="fas fa-leaf"></i>
                                </div>
                            </div>
                        </div>
                        <div class="service-content">
                            <h3>Hard Landscaping</h3>
                            <p>Professional hard landscaping solutions that enhance outdoor spaces with quality materials.</p>
                            <a href="services.php" class="service-link">
                                Explore <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Modern Projects Section -->
    <section class="modern-projects" id="modern-projects">
        <div class="container">
            <div class="section-header">
                <span class="section-badge">PORTFOLIO</span>
                <h2 class="section-title">Latest Projects</h2>
                <p class="section-subtitle">Every Project is Unique and Custom Made. We are proud to showcase a selection of our completed projects that demonstrate our expertise and commitment to delivering high-quality construction solutions.</p>
            </div>

            <div class="projects-grid">
                <?php if (!empty($featuredProjects)): ?>
                    <?php foreach ($featuredProjects as $project): ?>
                    <div class="project-card">
                        <div class="project-image">
                            <img src="<?= $project['featured_image'] ? UPLOAD_URL . '/' . $project['featured_image'] : ASSETS_URL . '/images/project-default.jpg' ?>"
                                 alt="<?= htmlspecialchars($project['title']) ?>">
                            <div class="project-overlay">
                                <div class="project-info">
                                    <span class="project-type"><?= ucfirst($project['project_type']) ?></span>
                                    <h3><?= htmlspecialchars($project['title']) ?></h3>
                                    <p class="project-description">Modern frame construction with sustainable materials</p>
                                    <a href="project.php?slug=<?= $project['slug'] ?>" class="project-link">
                                        View Project <i class="fas fa-arrow-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <!-- Default Projects if none in database -->
                    <div class="project-card">
                        <div class="project-image">
                            <img src="<?= ASSETS_URL ?>/images/becanham.jpg" alt="Becanham">
                            <div class="project-overlay">
                                <div class="project-info">
                                    <span class="project-type">Completed Projects</span>
                                    <h3>Becanham</h3>
                                    <p class="project-description">Professional construction with quality workmanship</p>
                                    <a href="projects.php" class="project-link">
                                        View Project <i class="fas fa-arrow-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="project-card">
                        <div class="project-image">
                            <img src="<?= ASSETS_URL ?>/images/walker-primary-school.jpg" alt="Walker Primary School">
                            <div class="project-overlay">
                                <div class="project-info">
                                    <span class="project-type">Educational</span>
                                    <h3>Walker Primary School</h3>
                                    <p class="project-description">Educational facility construction with modern standards</p>
                                    <a href="projects.php" class="project-link">
                                        View Project <i class="fas fa-arrow-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="project-card">
                        <div class="project-image">
                            <img src="<?= ASSETS_URL ?>/images/alton-sports-centre.jpg" alt="Alton Sports Centre">
                            <div class="project-overlay">
                                <div class="project-info">
                                    <span class="project-type">Sports Facility</span>
                                    <h3>Alton Sports Centre</h3>
                                    <p class="project-description">Modern sports facility with professional construction</p>
                                    <a href="projects.php" class="project-link">
                                        View Project <i class="fas fa-arrow-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="project-card">
                        <div class="project-image">
                            <img src="<?= ASSETS_URL ?>/images/addenbrookes-hospital.jpg" alt="Addenbrooke's Hospital">
                            <div class="project-overlay">
                                <div class="project-info">
                                    <span class="project-type">Healthcare</span>
                                    <h3>Addenbrooke's Hospital</h3>
                                    <p class="project-description">Healthcare facility construction with precision and quality</p>
                                    <a href="projects.php" class="project-link">
                                        View Project <i class="fas fa-arrow-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="project-card">
                        <div class="project-image">
                            <img src="<?= ASSETS_URL ?>/images/clapham-south.jpg" alt="Clapham South Thurleigh Road">
                            <div class="project-overlay">
                                <div class="project-info">
                                    <span class="project-type">Residential</span>
                                    <h3>Clapham South Thurleigh Road</h3>
                                    <p class="project-description">Residential development with modern construction techniques</p>
                                    <a href="projects.php" class="project-link">
                                        View Project <i class="fas fa-arrow-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="project-card">
                        <div class="project-image">
                            <img src="<?= ASSETS_URL ?>/images/arkley-barnet.jpg" alt="Arkley New Barnet Road">
                            <div class="project-overlay">
                                <div class="project-info">
                                    <span class="project-type">Residential</span>
                                    <h3>Arkley New Barnet Road</h3>
                                    <p class="project-description">Quality residential construction with professional expertise</p>
                                    <a href="projects.php" class="project-link">
                                        View Project <i class="fas fa-arrow-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <div class="section-footer">
                <a href="projects.php" class="btn-modern">View All Projects</a>
            </div>
        </div>
    </section>

    <!-- Modern Testimonials Section -->
    <section class="modern-testimonials" id="modern-testimonials">
        <div class="container">
            <div class="section-header">
                <span class="section-badge">CLIENT FEEDBACK</span>
                <h2 class="section-title">What Our Clients Say</h2>
                <p class="section-subtitle">Don't just take our word for it. Here's what our satisfied clients have to say about our construction services and commitment to excellence.</p>
            </div>

            <?php if (!empty($testimonials)): ?>
            <div class="testimonials-grid">
                <?php foreach ($testimonials as $testimonial): ?>
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <!-- Quote Icon -->
                        <div class="quote-icon">
                            <i class="fas fa-quote-left"></i>
                        </div>

                        <!-- Review Text -->
                        <div class="testimonial-text">
                            <p>"<?= htmlspecialchars($testimonial['review']) ?>"</p>
                        </div>

                        <!-- Rating Stars -->
                        <div class="testimonial-rating">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                <i class="fas fa-star<?= $i <= $testimonial['rating'] ? ' active' : '' ?>"></i>
                            <?php endfor; ?>
                        </div>
                    </div>

                    <!-- Client Info -->
                    <div class="testimonial-footer">
                        <div class="client-info">
                            <h4 class="client-name"><?= htmlspecialchars($testimonial['client_name']) ?></h4>
                            <?php if (!empty($testimonial['company'])): ?>
                                <p class="client-company"><?= htmlspecialchars($testimonial['company']) ?></p>
                            <?php endif; ?>
                        </div>

                        <!-- Verified Badge -->
                        <div class="verified-badge">
                            <i class="fas fa-check-circle"></i>
                            <span>Verified</span>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- Overall Rating Summary -->
            <div class="testimonials-summary">
                <div class="summary-card">
                    <div class="summary-rating">
                        <div class="rating-number">4.9</div>
                        <div class="rating-stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="rating-text">Based on <?= count($testimonials) ?>+ reviews</div>
                    </div>
                    <div class="summary-stats" data-stagger="150">
                        <div class="stat-item stagger-child hover-lift">
                            <span class="stat-number counter-element" data-target="100" data-suffix="%">100%</span>
                            <span class="stat-label">Client Satisfaction</span>
                        </div>
                        <div class="stat-item stagger-child hover-lift">
                            <span class="stat-number counter-element" data-target="15" data-suffix="+">15+</span>
                            <span class="stat-label">Years Experience</span>
                        </div>
                        <div class="stat-item stagger-child hover-lift">
                            <span class="stat-number counter-element" data-target="500" data-suffix="+">500+</span>
                            <span class="stat-label">Projects Completed</span>
                        </div>
                    </div>
                </div>
            </div>
            <?php else: ?>
            <!-- Default testimonials if none in database -->
            <div class="testimonials-grid">
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <div class="quote-icon">
                            <i class="fas fa-quote-left"></i>
                        </div>
                        <div class="testimonial-text">
                            <p>"Flori Construction exceeded our expectations with their professional approach and quality workmanship. The team was reliable, efficient, and delivered our project on time and within budget."</p>
                        </div>
                        <div class="testimonial-rating">
                            <i class="fas fa-star active"></i>
                            <i class="fas fa-star active"></i>
                            <i class="fas fa-star active"></i>
                            <i class="fas fa-star active"></i>
                            <i class="fas fa-star active"></i>
                        </div>
                    </div>
                    <div class="testimonial-footer">
                        <div class="client-info">
                            <h4 class="client-name">Sarah Johnson</h4>
                            <p class="client-company">Residential Client</p>
                        </div>
                        <div class="verified-badge">
                            <i class="fas fa-check-circle"></i>
                            <span>Verified</span>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <div class="quote-icon">
                            <i class="fas fa-quote-left"></i>
                        </div>
                        <div class="testimonial-text">
                            <p>"Outstanding civil engineering services. Their expertise in RC frame construction is evident in the quality of work delivered. Highly recommend for any construction project."</p>
                        </div>
                        <div class="testimonial-rating">
                            <i class="fas fa-star active"></i>
                            <i class="fas fa-star active"></i>
                            <i class="fas fa-star active"></i>
                            <i class="fas fa-star active"></i>
                            <i class="fas fa-star active"></i>
                        </div>
                    </div>
                    <div class="testimonial-footer">
                        <div class="client-info">
                            <h4 class="client-name">Michael Chen</h4>
                            <p class="client-company">Property Developer</p>
                        </div>
                        <div class="verified-badge">
                            <i class="fas fa-check-circle"></i>
                            <span>Verified</span>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <div class="quote-icon">
                            <i class="fas fa-quote-left"></i>
                        </div>
                        <div class="testimonial-text">
                            <p>"Professional groundworks and basement construction services. The team's attention to detail and commitment to quality is impressive. Would definitely work with them again."</p>
                        </div>
                        <div class="testimonial-rating">
                            <i class="fas fa-star active"></i>
                            <i class="fas fa-star active"></i>
                            <i class="fas fa-star active"></i>
                            <i class="fas fa-star active"></i>
                            <i class="fas fa-star active"></i>
                        </div>
                    </div>
                    <div class="testimonial-footer">
                        <div class="client-info">
                            <h4 class="client-name">Emma Thompson</h4>
                            <p class="client-company">Commercial Client</p>
                        </div>
                        <div class="verified-badge">
                            <i class="fas fa-check-circle"></i>
                            <span>Verified</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Overall Rating Summary -->
            <div class="testimonials-summary">
                <div class="summary-card">
                    <div class="summary-rating">
                        <div class="rating-number">4.9</div>
                        <div class="rating-stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="rating-text">Based on 100+ reviews</div>
                    </div>
                    <div class="summary-stats">
                        <div class="stat-item">
                            <span class="stat-number">100%</span>
                            <span class="stat-label">Client Satisfaction</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">15+</span>
                            <span class="stat-label">Years Experience</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">500+</span>
                            <span class="stat-label">Projects Completed</span>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Enhanced Modern Footer -->
    <footer class="enhanced-modern-footer">
        <div class="footer-background">
            <div class="footer-gradient"></div>
            <div class="footer-pattern"></div>
        </div>

        <div class="container">
            <!-- Top Navigation Bar -->
            <div class="footer-top-nav">
                <div class="footer-nav-left">
                    <?php if ($customLogo && isset($customLogo['logo_path']) && !empty($customLogo['logo_path'])): ?>
                        <img src="<?= UPLOAD_URL . '/' . $customLogo['logo_path'] ?>"
                             alt="<?= SITE_NAME ?>"
                             class="footer-logo-img"
                             onerror="this.style.display='none'; document.querySelector('.footer-default-logo').style.display='block';">
                        <span class="footer-default-logo" style="display: none;"><?= SITE_NAME ?></span>
                    <?php else: ?>
                        <span class="footer-brand-name"><?= SITE_NAME ?></span>
                    <?php endif; ?>
                </div>

                <div class="footer-nav-center">
                    <nav class="footer-nav-menu">
                        <a href="services.php" class="footer-nav-link">Services</a>
                        <a href="projects.php" class="footer-nav-link">Gallery</a>
                        <a href="about.php" class="footer-nav-link">About</a>
                    </nav>
                </div>

                <div class="footer-nav-right">
                    <a href="contact.php" class="footer-contact-link">Contact</a>
                </div>
            </div>

            <!-- Main Footer Content -->
            <div class="footer-main-content">
                <!-- Work With Us Section -->
                <div class="footer-cta-section">
                    <div class="footer-cta-left">
                        <h2 class="footer-cta-title">WORK WITH US</h2>
                        <div class="footer-cta-line"></div>
                    </div>
                    <div class="footer-cta-right">
                        <a href="mailto:<?= SITE_EMAIL ?>" class="footer-email-link">
                            <?= str_replace('@', '@', SITE_EMAIL) ?>
                        </a>
                    </div>
                </div>

                <!-- Footer Links Grid -->
                <div class="footer-links-grid">
                    <!-- Site Map -->
                    <div class="footer-links-column">
                        <h4 class="footer-column-title">SiteMap</h4>
                        <ul class="footer-links-list">
                            <li><a href="index.php">Home</a></li>
                            <li><a href="services.php">Services</a></li>
                            <li><a href="projects.php">Gallery</a></li>
                            <li><a href="about.php">Our Team</a></li>
                            <li><a href="contact.php">Contact</a></li>
                        </ul>
                    </div>

                    <!-- Social Links -->
                    <div class="footer-links-column">
                        <h4 class="footer-column-title">Social</h4>
                        <ul class="footer-links-list">
                            <li><a href="<?= INSTAGRAM_URL ?>" target="_blank">Instagram</a></li>
                            <li><a href="<?= FACEBOOK_URL ?>" target="_blank">Facebook</a></li>
                            <li><a href="<?= LINKEDIN_URL ?>" target="_blank">LinkedIn</a></li>
                            <li><a href="<?= YOUTUBE_URL ?>" target="_blank">YouTube</a></li>
                        </ul>
                    </div>

                    <!-- Contact Information -->
                    <div class="footer-contact-column">
                        <div class="footer-contact-info">
                            <div class="contact-item">
                                <span class="contact-label">Address:</span>
                                <span class="contact-value"><?= SITE_ADDRESS ?></span>
                            </div>
                            <div class="contact-item">
                                <span class="contact-label">Phone:</span>
                                <a href="tel:<?= str_replace(' ', '', SITE_PHONE) ?>" class="contact-value"><?= SITE_PHONE ?></a>
                            </div>
                            <div class="contact-item">
                                <span class="contact-label">Mobile:</span>
                                <a href="tel:<?= str_replace(' ', '', SITE_MOBILE) ?>" class="contact-value"><?= SITE_MOBILE ?></a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Large Brand Name -->
                <div class="footer-brand-section">
                    <h1 class="footer-large-brand">
                        <?= strtoupper(SITE_NAME) ?><sup class="brand-trademark">™</sup>
                    </h1>
                </div>
            </div>

            <!-- Footer Bottom -->
            <div class="footer-bottom-section">
                <div class="footer-bottom-content">
                    <p class="footer-copyright">
                        &copy; <?= date('Y') ?> <?= SITE_NAME ?>. All rights reserved.
                    </p>
                    <div class="footer-bottom-links">
                        <a href="#" class="footer-bottom-link">Privacy Policy</a>
                        <a href="#" class="footer-bottom-link">Terms of Service</a>
                        <a href="#" class="footer-bottom-link">Cookies</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Showreel Modal -->
    <div id="showreel-modal" class="modal">
        <div class="modal-content">
            <span class="modal-close">&times;</span>
            <div class="video-container">
                <!-- YouTube Embed instead of direct video -->
                <iframe id="showreel-video"
                        width="100%"
                        height="450"
                        src="https://www.youtube.com/embed/cqacADHvUSw"
                        title="Flori Construction Showreel"
                        frameborder="0"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowfullscreen>
                </iframe>
                <!-- Fallback message -->
                <div class="video-fallback" style="display: none;">
                    <p>Video unavailable. Please visit our <a href="https://www.youtube.com/watch?v=cqacADHvUSw" target="_blank">YouTube channel</a> to view our showreel.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced JavaScript -->
    <script src="<?= ASSETS_URL ?>/js/main.js"></script>
    <script>
        // Enhanced functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize all enhanced features
            window.initHeroAnimations();
            window.initScrollEffects();
            window.initInteractiveElements();
            window.initShowreel();
            window.initMobileMenu();
            window.initParticles();

            // Initialize new smooth scroll features
            if (typeof window.initSmoothScrolling === 'function') {
                window.initSmoothScrolling();
            }
            if (typeof window.initEnhancedNavigation === 'function') {
                window.initEnhancedNavigation();
            }
            if (typeof window.initScrollEnhancements === 'function') {
                window.initScrollEnhancements();
            }

            // Initialize new animation features
            if (typeof window.initSectionRevealAnimations === 'function') {
                window.initSectionRevealAnimations();
            }
            if (typeof window.initAdvancedAnimations === 'function') {
                window.initAdvancedAnimations();
            }
        });

        // Hero animations with stagger effect
        window.initHeroAnimations = function() {
            const titleLines = document.querySelectorAll('.title-line');
            titleLines.forEach((line, index) => {
                const delay = line.dataset.delay || 0;
                setTimeout(() => {
                    line.style.opacity = '1';
                    line.style.transform = 'translateY(0)';
                }, delay);
            });

            // Badge animation with error handling
            setTimeout(() => {
                const badge = document.querySelector('.hero-badge');
                if (badge) {
                    badge.classList.add('animate');
                }
            }, 500);

            // Specs animation with error handling
            setTimeout(() => {
                const specs = document.querySelector('.house-specs');
                if (specs) {
                    specs.classList.add('animate');
                }
            }, 800);
        };

        // Enhanced scroll effects and navigation
        window.initScrollEffects = function() {
            const navDots = document.querySelectorAll('.nav-dot');
            const sections = document.querySelectorAll('section[id]');
            const headerNavLinks = document.querySelectorAll('.nav-link');
            const header = document.querySelector('.modern-header');

            // Parallax effect for hero background with throttling
            let ticking = false;
            function updateParallax() {
                const scrolled = window.pageYOffset;
                const heroImage = document.querySelector('.hero-image');
                if (heroImage) {
                    heroImage.style.transform = `translateY(${scrolled * 0.5}px)`;
                }

                // Header scroll effect
                if (header) {
                    if (scrolled > 100) {
                        header.classList.add('scrolled');
                    } else {
                        header.classList.remove('scrolled');
                    }
                }

                // Update navigation dots and header nav
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.clientHeight;
                    if (scrolled >= sectionTop - 200) {
                        current = section.getAttribute('id');
                    }
                });

                // Update navigation dots
                navDots.forEach(dot => {
                    dot.classList.remove('active');
                    if (dot.dataset.section === current) {
                        dot.classList.add('active');
                    }
                });

                // Update header navigation
                headerNavLinks.forEach(link => {
                    link.classList.remove('active');
                    if (current === 'modern-hero' && link.getAttribute('href') === 'index.php') {
                        link.classList.add('active');
                    }
                });

                ticking = false;
            }

            window.addEventListener('scroll', () => {
                if (!ticking) {
                    requestAnimationFrame(updateParallax);
                    ticking = true;
                }
            });

            // Navigation dot clicks with smooth scrolling
            navDots.forEach(dot => {
                dot.addEventListener('click', () => {
                    const targetSection = document.getElementById(dot.dataset.section);
                    if (targetSection && header) {
                        const headerHeight = header.offsetHeight;
                        const targetPosition = targetSection.offsetTop - headerHeight;

                        window.scrollTo({
                            top: targetPosition,
                            behavior: 'smooth'
                        });
                    }
                });
            });
        };

        // Interactive elements
        window.initInteractiveElements = function() {
            // Hotspot interactions
            const hotspots = document.querySelectorAll('.hotspot');
            hotspots.forEach(hotspot => {
                hotspot.addEventListener('mouseenter', function() {
                    const tooltip = document.createElement('div');
                    tooltip.className = 'hotspot-tooltip';
                    tooltip.textContent = this.dataset.info;
                    this.appendChild(tooltip);
                });

                hotspot.addEventListener('mouseleave', function() {
                    const tooltip = this.querySelector('.hotspot-tooltip');
                    if (tooltip) tooltip.remove();
                });
            });

            // Floating stats animation
            const statNumbers = document.querySelectorAll('.stat-number');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        animateNumber(entry.target);
                    }
                });
            });

            statNumbers.forEach(stat => observer.observe(stat));
        };

        // Number animation
        window.animateNumber = function(element) {
            const target = element.textContent;
            const isPercentage = target.includes('%');
            const isPlus = target.includes('+');
            const number = parseInt(target.replace(/[^\d]/g, ''));

            let current = 0;
            const increment = number / 50;
            const timer = setInterval(() => {
                current += increment;
                if (current >= number) {
                    current = number;
                    clearInterval(timer);
                }

                let display = Math.floor(current);
                if (isPercentage) display += '%';
                if (isPlus) display += '+';

                element.textContent = display;
            }, 30);
        };

        // Enhanced showreel functionality
        window.initShowreel = function() {
            const modal = document.getElementById('showreel-modal');
            const closeBtn = document.querySelector('.modal-close');
            const iframe = document.getElementById('showreel-video');
            const fallback = document.querySelector('.video-fallback');

            window.playShowreel = function() {
                if (modal) {
                    modal.style.display = 'block';
                    document.body.style.overflow = 'hidden';

                    // Add autoplay to iframe src
                    if (iframe) {
                        const currentSrc = iframe.src;
                        if (!currentSrc.includes('autoplay=1')) {
                            iframe.src = currentSrc + '?autoplay=1';
                        }
                    }
                }
            };

            function closeModal() {
                if (modal) {
                    modal.style.display = 'none';
                    document.body.style.overflow = '';

                    // Reset iframe to stop video
                    if (iframe) {
                        const src = iframe.src;
                        iframe.src = '';
                        iframe.src = src.replace('?autoplay=1', '').replace('&autoplay=1', '');
                    }
                }
            }

            // Error handling for iframe loading
            if (iframe) {
                iframe.onerror = function() {
                    if (fallback) {
                        iframe.style.display = 'none';
                        fallback.style.display = 'block';
                    }
                };
            }

            if (closeBtn) {
                closeBtn.onclick = closeModal;
            }

            window.onclick = function(event) {
                if (event.target === modal) closeModal();
            };

            // Keyboard support
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && modal && modal.style.display === 'block') {
                    closeModal();
                }
            });
        };

        // Enhanced mobile menu
        window.initMobileMenu = function() {
            const mobileToggle = document.querySelector('.mobile-menu-toggle');
            const headerNav = document.querySelector('.header-nav');

            if (mobileToggle && headerNav) {
                mobileToggle.addEventListener('click', function() {
                    headerNav.classList.toggle('active');
                    mobileToggle.classList.toggle('active');
                    document.body.classList.toggle('menu-open');
                });

                // Close menu when clicking on links
                const navLinks = headerNav.querySelectorAll('a');
                navLinks.forEach(link => {
                    link.addEventListener('click', () => {
                        headerNav.classList.remove('active');
                        mobileToggle.classList.remove('active');
                        document.body.classList.remove('menu-open');
                    });
                });
            }
        };

        // Particle animation
        window.initParticles = function() {
            const particles = document.querySelectorAll('.particle');
            particles.forEach((particle, index) => {
                // Random initial position and animation
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 10 + 's';
                particle.style.animationDuration = (Math.random() * 20 + 10) + 's';
            });
        };

        // Enhanced smooth scroll for all internal links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const headerHeight = document.querySelector('.modern-header').offsetHeight;
                    const targetPosition = target.offsetTop - headerHeight;

                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Add active state management for navigation
        window.updateActiveNavigation = function() {
            const currentPage = window.location.pathname.split('/').pop() || 'index.php';
            const navLinks = document.querySelectorAll('.nav-link');

            navLinks.forEach(link => {
                link.classList.remove('active');
                const linkHref = link.getAttribute('href');
                if (linkHref === currentPage ||
                    (currentPage === '' && linkHref === 'index.php') ||
                    (currentPage === 'index.php' && linkHref === 'index.php')) {
                    link.classList.add('active');
                }
            });
        };

        // Initialize active navigation on page load
        window.updateActiveNavigation();

        // Debug: Test function availability (remove in production)
        window.testFunctions = function() {
            const functions = ['initHeroAnimations', 'initScrollEffects', 'initInteractiveElements', 'initShowreel', 'initMobileMenu', 'initParticles', 'animateNumber', 'updateActiveNavigation', 'showNotification', 'debounce', 'throttle'];
            const results = functions.map(fn => `${fn}: ${typeof window[fn] === 'function' ? '✅' : '❌'}`);
            console.log('Function Availability Test:', results);
            if (typeof window.showNotification === 'function') {
                window.showNotification('Function test completed! Check console for details.', 'success');
            }
            return results;
        };

        // Auto-test on load (for debugging)
        setTimeout(() => {
            if (window.location.search.includes('debug=true')) {
                window.testFunctions();
            }
        }, 1000);
    </script>

    <script>
    // Service navigation functionality for About Section
    let currentServiceIndex = 0;
    const services = <?= json_encode($featuredServices) ?>;

    function navigateServices(direction) {
        if (!services || services.length === 0) return;

        if (direction === 'next') {
            currentServiceIndex = (currentServiceIndex + 1) % services.length;
        } else {
            currentServiceIndex = (currentServiceIndex - 1 + services.length) % services.length;
        }

        updateServiceDisplay();
    }

    function updateServiceDisplay() {
        if (!services || services.length === 0) return;

        const service = services[currentServiceIndex];
        const nextService = services[(currentServiceIndex + 1) % services.length];

        // Update caption
        const titleElement = document.querySelector('.bottom-section .caption .title');
        const locationElement = document.querySelector('.bottom-section .caption .location');

        if (titleElement) titleElement.textContent = service.title;
        if (locationElement) locationElement.textContent = service.short_description;

        // Update images
        const images = document.querySelectorAll('.bottom-section .images-container img');
        if (images.length >= 1) {
            const serviceImage = service.featured_image ?
                '<?= UPLOAD_URL ?>/' + service.featured_image :
                '<?= ASSETS_URL ?>/images/service-default.jpg';
            images[0].src = serviceImage;
            images[0].alt = service.title;
            images[0].setAttribute('data-service-id', service.id);
        }

        if (images.length >= 2 && nextService) {
            const nextServiceImage = nextService.featured_image ?
                '<?= UPLOAD_URL ?>/' + nextService.featured_image :
                '<?= ASSETS_URL ?>/images/service-default.jpg';
            images[1].src = nextServiceImage;
            images[1].alt = nextService.title;
            images[1].setAttribute('data-service-id', nextService.id);
        }
    }
    </script>
</body>
</html>
